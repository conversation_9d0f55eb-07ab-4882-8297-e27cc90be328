import { GoogleGenAI } from '@google/genai';
import { action, internalAction } from "./_generated/server";
import { v } from "convex/values";
import { internal, api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// ============================================================================
// CONFIGURATION VARIABLES - EASILY CUSTOMIZABLE
// ============================================================================

// API Keys
const SERPER_API_KEY = "1b054aa64cee740498d032ce7917c22766a5d2ac";
const GEMINI_API_KEY = "AIzaSyBaDWf_3ly9xZM26kYM3qppNpbOqwuiaTE";
const model="gemini-2.0-flash";

// AI Model Configuration (kept for reference but using direct model name in fcgem.js style)
// const AI_MODELS = {
//   MAIN_MODEL: model,           // Primary model for search and function calling
//   EVALUATION_MODEL: model,// Lighter model for round evaluation
//   FINAL_RESPONSE_MODEL: model   // Model for generating final responses
// };

// Search Round Configuration (kept for reference but not used in fcgem.js logic)
// const SEARCH_ROUNDS = {
//   ABSOLUTE_MAX_ROUNDS: 20,        // Hard limit to prevent infinite loops
//   GUARANTEED_ROUNDS: 3,           // Always continue for first N rounds
//   FALLBACK_MAX_ROUNDS: 8,         // Fallback limit if AI evaluation fails
//   AI_CONFIDENCE_THRESHOLD: 6      // Minimum confidence (1-10) to continue rounds
// };

// Progress Tracking Configuration
const PROGRESS_CONFIG = {
  INITIAL_PROGRESS: 10,           // Starting progress percentage
  ROUND_PROGRESS_INCREMENT: 15,   // Progress increase per round
  MAX_ROUND_PROGRESS: 80,         // Maximum progress from rounds
  SCRAPING_BASE_PROGRESS: 40,     // Base progress when scraping starts
  SCRAPING_INCREMENT: 10,         // Progress increase per scraped source
  MAX_SCRAPING_PROGRESS: 75,      // Maximum progress from scraping
  FINALIZING_PROGRESS: 90,        // Progress when generating final response
  COMPLETE_PROGRESS: 100          // Final completion progress
};

// Search Step Thresholds (kept for reference but not used in fcgem.js logic)
// const STEP_THRESHOLDS = {
//   SEARCHING_ROUNDS: 2,            // Rounds 1-2: "searching"
//   ANALYZING_ROUNDS: 4,            // Rounds 3-4: "analyzing"
//   // Rounds 5+: "processing"
// };

// Default Search Parameters
const SEARCH_DEFAULTS = {
  NUM_RESULTS: 10,                // Default number of search results
  REGION: 'us',                   // Default region code
  LANGUAGE: 'en'                  // Default language code
};

// Session Configuration
const SESSION_CONFIG = {
  TITLE_MAX_LENGTH: 50,           // Maximum length for auto-generated session titles
  TITLE_TRUNCATE_SUFFIX: "..."    // Suffix for truncated titles
};

// Function Calling Configuration (kept for reference but using direct values)
// const FUNCTION_CONFIG = {
//   MODE: 'ANY'                     // Function calling mode for Gemini
// };

// Gmail Configuration
const GMAIL_CONFIG = {
  DEFAULT_MAX_RESULTS: 10,        // Default number of emails to fetch
  MAX_RESULTS_LIMIT: 50,          // Maximum allowed emails per request
  DEFAULT_INCLUDE_BODY: true,     // Include email body by default
  SCOPES: [                       // Required Gmail API scopes
    'https://www.googleapis.com/auth/gmail.readonly'
  ]
};

// Configure the AI client
const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });

// Define function declarations for Gemini (exactly as in your code)
const functionDeclarations = [
  {
    name: "search_web",
    description: "Search the web using Serper API to get current information",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "The search query"
        },
        num: {
          type: "number",
          description: "Number of results to return (default: 10)"
        },
        gl: {
          type: "string",
          description: "Region code (default: 'us')"
        },
        hl: {
          type: "string",
          description: "Language code (default: 'en')"
        }
      },
      required: ["query"]
    }
  },
  {
    name: "scrape_webpage",
    description: "Scrape content from a specific webpage URL",
    parameters: {
      type: "object",
      properties: {
        url: {
          type: "string",
          description: "The URL to scrape"
        }
      },
      required: ["url"]
    }
  },
  {
    name: "fetch_gmail",
    description: "Fetch emails from the user's Gmail account. Use this when the user asks about their emails, recent messages, or specific email content. Requires user authentication.",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "Search query to filter emails (e.g., 'from:<EMAIL>', 'subject:meeting', 'is:unread', 'newer_than:7d')"
        },
        maxResults: {
          type: "number",
          description: "Maximum number of emails to fetch (default: 10, max: 50)"
        },
        includeBody: {
          type: "boolean",
          description: "Whether to include email body content (default: true)"
        }
      },
      required: ["query"]
    }
  }
];

// Function to perform web search using Serper API (exactly as in your code)
async function performSerperSearch({
  query,
  num = SEARCH_DEFAULTS.NUM_RESULTS,
  gl = SEARCH_DEFAULTS.REGION,
  hl = SEARCH_DEFAULTS.LANGUAGE
}: any) {
  console.log(`[SEARCH] Searching for: "${query}"`);

  const response = await fetch('https://google.serper.dev/search', {
    method: 'POST',
    headers: {
      'X-API-KEY': SERPER_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      q: query,
      num: num,
      gl: gl,
      hl: hl,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Serper API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const result = await response.json();
  console.log(`[SEARCH] Found ${result.organic?.length || 0} results`);
  return result;
}

// Function to scrape webpage content using Serper API (exactly as in your code)
async function performSerperScrape({ url }: any) {
  console.log(`[SCRAPE] Scraping: ${url}`);

  const response = await fetch('https://scrape.serper.dev', {
    method: 'POST',
    headers: {
      'X-API-KEY': SERPER_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      url: url,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Serper Scrape API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const result = await response.json();
  console.log(`[SCRAPE] Scraped ${result.text?.length || 0} characters`);
  return result;
}

// Function to fetch Gmail emails (requires user authentication)
async function performGmailFetch({
  query,
  maxResults = GMAIL_CONFIG.DEFAULT_MAX_RESULTS,
  includeBody = GMAIL_CONFIG.DEFAULT_INCLUDE_BODY,
  userId,
  ctx
}: any) {
  console.log(`[GMAIL] Fetching emails with query: "${query}"`);

  // Validate maxResults
  const validMaxResults = Math.min(maxResults, GMAIL_CONFIG.MAX_RESULTS_LIMIT);

  try {
    // Use the real Gmail implementation with direct userId
    // Since we're in an internalAction context, we need to pass userId directly
    const result = await ctx.runAction(api.gmailActions.fetchGmailEmailsWithUserId, {
      query: query,
      maxResults: validMaxResults,
      includeBody: includeBody,
      userId: userId as any
    });

    console.log(`[GMAIL] Fetched ${result.emails?.length || 0} emails for user ${userId || 'unknown'}`);
    return result;

  } catch (error: any) {
    console.error(`[GMAIL] Error fetching emails:`, error.message);

    // Return a graceful error response instead of throwing
    return {
      emails: [],
      totalCount: 0,
      message: `Gmail error: ${error.message}`,
      authRequired: error.message.includes('authenticate') || error.message.includes('expired'),
      query: query,
      maxResults: validMaxResults,
      success: false
    };
  }
}



// Function to evaluate if more rounds are needed using AI (kept for reference but not used in fcgem.js logic)
// async function shouldContinueRounds(conversationHistory: any, round: number) {
//   // Safety mechanism: absolute maximum to prevent infinite loops
//   if (round >= SEARCH_ROUNDS.ABSOLUTE_MAX_ROUNDS) {
//     console.log(`Reached absolute maximum of ${SEARCH_ROUNDS.ABSOLUTE_MAX_ROUNDS} rounds - stopping for safety`);
//     return false;
//   }

//   // For the first few rounds, always continue if there are function calls
//   if (round <= SEARCH_ROUNDS.GUARANTEED_ROUNDS) {
//     return true;
//   }

//   // Ask the AI model to evaluate if more rounds are needed
//   try {
//     const evaluationPrompt = `
// Based on the conversation history and the information gathered so far, determine if more rounds of tool calls are needed to fully answer the original query.

// Consider:
// 1. Have we gathered sufficient information to provide a comprehensive answer?
// 2. Are there any gaps in the information that require additional searches or scraping?
// 3. Is the current information accurate and complete enough?
// 4. Would additional tool calls significantly improve the response quality?

// Respond with a JSON object containing:
// {
//   "shouldContinue": true/false,
//   "reasoning": "Brief explanation of why more rounds are or aren't needed",
//   "confidence": 1-10 (how confident you are in this decision)
// }

// Current round: ${round}
// `;

//     const response = await ai.models.generateContent({
//       model: AI_MODELS.EVALUATION_MODEL,
//       contents: [
//         ...conversationHistory,
//         {
//           role: "user",
//           parts: [{ text: evaluationPrompt }]
//         }
//       ],
//     });

//     const responseText = response.text || "{}";
//     const evaluation = JSON.parse(responseText.replace(/```json\n?|\n?```/g, ''));

//     console.log(`\n=== AI EVALUATION (Round ${round}) ===`);
//     console.log(`Should continue: ${evaluation.shouldContinue}`);
//     console.log(`Reasoning: ${evaluation.reasoning}`);
//     console.log(`Confidence: ${evaluation.confidence}/10`);

//     return evaluation.shouldContinue && evaluation.confidence >= SEARCH_ROUNDS.AI_CONFIDENCE_THRESHOLD;
//   } catch (error) {
//     console.error("Error in AI evaluation:", error);
//     // Fallback: continue if we're under a reasonable limit
//     return round < SEARCH_ROUNDS.FALLBACK_MAX_ROUNDS;
//   }
// }

// Function to generate simple summary (exactly as in fcgem.js)
async function generateSimpleSummary(functionResults: any, chat: any) {
    try {
        console.log("\n" + "=".repeat(50));
        console.log("GENERATING FINAL SUMMARY");
        console.log("=".repeat(50));

        // Prepare the summary prompt with all function results
        let summaryPrompt = "Based on the following function call results, please provide a comprehensive summary:\n\n";

        summaryPrompt += `
INSTRUCTIONS:
Please provide a comprehensive response to the user's original query based on all the data collected above.
- Organize information logically and clearly
- Include specific details and findings from the gathered information
- Ensure accuracy by using only the information collected during the search/scrape process
- Make the response complete and actionable for the user's needs`;

        // Disable function calling for summary generation (exactly as in fcgem.js)
        const originalConfig = chat.config;
        chat.config = {
            tools: [{
                functionDeclarations: functionDeclarations
            }],
            toolConfig: {
                functionCallingConfig: {
                    mode: 'NONE'
                }
            }
        };

        console.log("Requesting AI summary...");
        const summaryResponse = await chat.sendMessage({
            message: summaryPrompt
        });

        // Restore original configuration
        chat.config = originalConfig;

        console.log("\n" + "=".repeat(50));
        console.log("FINAL SUMMARY");
        console.log("=".repeat(50));
        console.log(summaryResponse.text);
        console.log("=".repeat(50));

        return summaryResponse.text;

    } catch (error) {
        console.error("Error generating summary:", error);

        // Fallback: provide a basic summary
        console.log("\n" + "=".repeat(50));
        console.log("BASIC SUMMARY (AI summary failed)");
        console.log("=".repeat(50));

        return "Summary generation failed. Please review the collected data above.";
    }
}

const config = {
            tools: [{
                functionDeclarations: functionDeclarations as any
            }],
            // Force the model to call 'any' function, instead of chatting.
            toolConfig: {
                functionCallingConfig: {
                    mode: 'any' as any
                }
            }
        };

// Function to run simple search with query (exactly as in fcgem.js)
async function runSimpleSearchWithQuery(query: string, ctx?: any, searchId?: string, userId?: string) {
    try {
        // Create a chat session (exactly as in fcgem.js)
        const chat = ai.chats.create({
            model: model,
            config: config as any
        });

        console.log("Simple Search and Scrape System:");
        console.log("=================================");
        console.log(`Query: ${query}`);
        console.log("=================================");

        const allFunctionResults: any[] = [];
        let sourcesScraped = 0;

        // Send the query to the model (exactly as in fcgem.js)
        const message = `User Query: ${query}

INSTRUCTIONS:
Please analyze the user's query and perform the necessary search and scrape operations to gather comprehensive information.
You can use the available functions (search_web and scrape_webpage) to collect all the required data.
Focus on providing complete and accurate information for the user's request.`;

        console.log("Sending query to AI model...");
        let response = await chat.sendMessage({
            message: message
        });

        let shouldContinue = true;
        let evaluationResponse: any;

        while(shouldContinue){

            if (response.functionCalls && response.functionCalls.length > 0) {
                console.log(`AI requested ${response.functionCalls.length} function calls`);

                for (const fn of response.functionCalls) {
                    const args = Object.entries(fn.args || {})
                        .map(([key, val]) => `${key}=${val}`)
                        .join(', ');
                    console.log(`Executing: ${fn.name}(${args})`);

                    // Execute the function calls and collect results
                    let result;
                    try {
                        if (fn.name === 'search_web') {
                            result = await performSerperSearch(fn.args);
                            console.log(`\x1b[32mSearch completed: ${JSON.stringify(result, null, 2)} \x1b[0m`);

                            console.log(`Search completed: Found ${result.organic?.length || 0} results`);

                            const funcResult = {
                                functionName: fn.name,
                                args: fn.args,
                                result: result,
                                success: true
                            };

                            allFunctionResults.push(funcResult);

                        } else if (fn.name === 'scrape_webpage') {
                            // Update progress for scraping
                            sourcesScraped++;
                            if (searchId && ctx) {
                                await ctx.runMutation(internal.queries.updateSearchProgress, {
                                    searchId,
                                    step: "scraping",
                                    progress: Math.min(40 + (sourcesScraped * 10), 80),
                                    message: `Scraping source ${sourcesScraped}...`,
                                    sourceUrl: fn.args?.url,
                                    sourceTitle: `Source ${sourcesScraped}`
                                });
                            }

                            result = await performSerperScrape(fn.args);
                            //log result in green
                            console.log(`\x1b[32mScrape completed: ${JSON.stringify(result, null, 2)} \x1b[0m`);
                            console.log(`Scrape completed: ${result.text?.length || 0} characters extracted`);

                            const funcResult = {
                                functionName: fn.name,
                                args: fn.args,
                                result: result,
                                success: true
                            };

                            allFunctionResults.push(funcResult);

                        } else if (fn.name === 'fetch_gmail') {
                            result = await performGmailFetch({ ...fn.args, userId: userId || ctx?.userId || 'current_user', ctx });
                            console.log(`Gmail fetch completed: ${result.emails?.length || 0} emails`);

                            const funcResult = {
                                functionName: fn.name,
                                args: fn.args,
                                result: result,
                                success: true
                            };

                            allFunctionResults.push(funcResult);
                        }
                    } catch (error: any) {
                        console.error(`Error executing ${fn.name}:`, error.message);

                        const funcResult = {
                            functionName: fn.name,
                            args: fn.args,
                            error: error.message,
                            success: false
                        };

                        allFunctionResults.push(funcResult);
                    }
                }

                console.log(`Completed ${allFunctionResults.length} operations`);
                console.log(`allFunctionResults:${JSON.stringify(allFunctionResults, null, 2)}`);

            } else {
                console.log("No function calls were made");
            }

            //send the results back to the chat with a prompt to that asks for a should continue parameter in json
            const functionResultsText = allFunctionResults.map((funcResult, index) => {
                return `${index}. ${JSON.stringify(funcResult, null, 2)}`;
            }).join('\n\n');
            const evaluationPrompt = `
            Based on the conversation history and the information gathered so far, determine if more rounds of tool calls are needed to fully answer the original query.

            Consider:
            1. The original query and its context
            2. The information gathered from previous tool calls
            3. The relevance and comprehensiveness of the gathered information

            Provide your evaluation in the below exact  JSON format:
            {
                "shouldContinue": true/false,
                "reasoning": "Brief explanation of why more rounds are or aren't needed",
                "confidence": 1-10 (how confident you are in this decision)
            }
            
            NEVER PROVIDE THE ANSWER IN ANY OTHER FORMAT.

            Function call results:\n\n${functionResultsText}
            `;
            console.log(`\n${"=".repeat(50)}`);
            console.log("Evaluating if more rounds are needed");
            console.log(`${"=".repeat(50)}`);

            // Temporarily disable function calling for evaluation (exactly as in fcgem.js)
            const originalConfig = (chat as any).config;
            (chat as any).config = {
                tools: [{
                    functionDeclarations: functionDeclarations
                }],
                toolConfig: {
                    functionCallingConfig: {
                        mode: 'NONE'
                    }
                }
            };

            evaluationResponse = await chat.sendMessage({
                message: evaluationPrompt
            });

            // Restore original configuration (exactly as in fcgem.js)
            (chat as any).config = originalConfig;

            // get the should continue parameter from response
            let evaluationText = evaluationResponse.text || '';
            if (!evaluationText) {
                console.log("No text response from evaluation, stopping...");
                shouldContinue = false;
            } else {
                try {
                    let evaluation = JSON.parse(evaluationText.replace(/```json\n?|\n?```/g, ''));
                    shouldContinue = evaluation.shouldContinue;
                } catch (parseError) {
                    console.log("Failed to parse evaluation response, stopping...");
                    shouldContinue = false;
                }
            }

            //exit loop if false
            if (!shouldContinue) {
                break;
            }

            // send a message to chat to continue search and scrape for more to gather more information
            const continuePrompt = `
            Based on these results collected:${functionResultsText}

            ,continue searching for more areas to find answers to the users original query,
            search the un explored areas for more data.

            Consider:
            1. The original query and its context
            2. The information gathered from previous tool calls
            3. The relevance and comprehensiveness of the gathered information

            `;

            response = await chat.sendMessage({
                message: continuePrompt
            });
        }

        console.log(`\n${"=".repeat(50)}`);
        console.log("Evaluation Response");
        console.log(`${"=".repeat(50)}`);
        if (evaluationResponse && evaluationResponse.text) {
            console.log(evaluationResponse.text);
        } else {
            console.log("No evaluation response text available");
        }
        console.log(`\n${"=".repeat(50)}`);


        // Generate final summary based on all function results
        console.log(`\n${"=".repeat(50)}`);
        console.log("Generating Final Summary");
        console.log(`${"=".repeat(50)}`);

        let finalSummary = "No results to summarize";
        if (allFunctionResults.length > 0) {
            finalSummary = await generateSimpleSummary(allFunctionResults, chat);
        } else {
            console.log("No results to summarize");
        }

        return {
            allFunctionResults,
            evaluationResponse: evaluationResponse?.text,
            finalSummary
        };

    } catch (error) {
        console.error("Error in simple search:", error);
        throw error;
    }
}

// Function to handle conversation with multiple rounds of function calling (updated to use fcgem.js logic)
// async function handleConversation(userPrompt: string, conversationHistory: any[] = []) {
//   try {
//     // Use the exact logic from fcgem.js
//     const result = await runSimpleSearchWithQuery(userPrompt);

//     // Convert the result to the expected conversation history format
//     conversationHistory.push({
//       role: "user",
//       parts: [{ text: userPrompt }]
//     });

//     conversationHistory.push({
//       role: "model",
//       parts: [{ text: result.finalSummary || "No response generated" }]
//     });

//     return conversationHistory;
//   } catch (error) {
//     console.error("Error in handleConversation:", error);

//     // Add error response to conversation history
//     conversationHistory.push({
//       role: "user",
//       parts: [{ text: userPrompt }]
//     });

//     conversationHistory.push({
//       role: "model",
//       parts: [{ text: `Error processing request: ${error instanceof Error ? error.message : 'Unknown error'}` }]
//     });

//     return conversationHistory;
//   }
// }

// Enhanced handleConversation with progress tracking (using fcgem.js logic)
async function handleConversationWithProgress(
  userPrompt: string,
  searchId: string,
  ctx: any,
  conversationHistory: any[] = [],
  userId?: string
) {
  try {
    // Initial progress update
    console.log(`[PROGRESS] Updating progress for searchId: ${searchId} - searching (${PROGRESS_CONFIG.INITIAL_PROGRESS}%)`);
    await ctx.runMutation(internal.queries.updateSearchProgress, {
      searchId,
      step: "searching",
      progress: PROGRESS_CONFIG.INITIAL_PROGRESS,
      message: "Starting AI-powered search with fcgem.js logic..."
    });

    // Progress update for processing
    await ctx.runMutation(internal.queries.updateSearchProgress, {
      searchId,
      step: "processing",
      progress: 50,
      message: "AI analyzing and gathering information..."
    });

    const result = await runSimpleSearchWithQuery(userPrompt, ctx, searchId, userId);

    // Progress update for finalizing
    await ctx.runMutation(internal.queries.updateSearchProgress, {
      searchId,
      step: "finalizing",
      progress: PROGRESS_CONFIG.FINALIZING_PROGRESS,
      message: "Generating comprehensive final response..."
    });

    // Convert the result to the expected conversation history format
    conversationHistory.push({
      role: "user",
      parts: [{ text: userPrompt }]
    });

    conversationHistory.push({
      role: "model",
      parts: [{ text: result.finalSummary || "No response generated" }]
    });

    // Don't mark as complete here since we're returning results directly now

    // Return the updated conversation history and metadata
    return {
      conversationHistory,
      totalToolCalls: result.allFunctionResults.length,
      sourcesScraped: result.allFunctionResults.filter((r: any) => r.functionName === 'scrape_webpage').length,
      rounds: 1, // fcgem.js logic handles rounds internally
      finalResponse: result.finalSummary || "No response generated"
    };
  } catch (error) {
    console.error("Error in handleConversationWithProgress:", error);

    // Update progress to show error
    await ctx.runMutation(internal.queries.updateSearchProgress, {
      searchId,
      step: "error",
      progress: 0,
      message: `Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    });

    // Add error response to conversation history
    conversationHistory.push({
      role: "user",
      parts: [{ text: userPrompt }]
    });

    conversationHistory.push({
      role: "model",
      parts: [{ text: `Error processing request: ${error instanceof Error ? error.message : 'Unknown error'}` }]
    });

    return {
      conversationHistory,
      totalToolCalls: 0,
      sourcesScraped: 0,
      rounds: 0,
      finalResponse: `Error processing request: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}



// Background search action that runs independently of client connection
export const runBackgroundSearch = internalAction({
  args: {
    query: v.string(),
    searchId: v.string(),
    sessionId: v.optional(v.string()),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    console.log(`[BACKGROUND] Starting background search for searchId: ${args.searchId}, query: "${args.query}", sessionId: ${args.sessionId}`);

    try {
      let conversationHistory: any[] = [];
      let currentSessionId = args.sessionId;

      // If sessionId is provided, try to load existing conversation history
      let existingSession = null;
      if (currentSessionId) {
        existingSession = await ctx.runMutation(internal.queries.getSessionForUser, {
          sessionId: currentSessionId,
          userId: args.userId
        });

        if (existingSession) {
          try {
            conversationHistory = JSON.parse(existingSession.conversationHistory);
            console.log(`[SESSION] Loaded ${conversationHistory.length} messages from existing session ${currentSessionId}`);
          } catch (error) {
            console.error("Error parsing conversation history:", error);
            conversationHistory = [];
          }
        } else {
          console.log(`[SESSION] Session ${currentSessionId} not found in database, will create new session`);
        }
      } else {
        // Create new session
        currentSessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        console.log(`[SESSION] Creating new session: ${currentSessionId}`);
      }

      // Use conversation logic with existing history
      const result = await handleConversationWithProgress(
        args.query,
        args.searchId,
        ctx,
        conversationHistory,
        args.userId
      );

      // Generate session title from first query if this is a new session
      let sessionTitle = undefined;
      if (!existingSession) {
        sessionTitle = args.query.length > SESSION_CONFIG.TITLE_MAX_LENGTH ?
          args.query.substring(0, SESSION_CONFIG.TITLE_MAX_LENGTH - SESSION_CONFIG.TITLE_TRUNCATE_SUFFIX.length) + SESSION_CONFIG.TITLE_TRUNCATE_SUFFIX :
          args.query;
      }

      // Save or update session with conversation history based on whether session exists in database
      if (!existingSession) {
        console.log(`[SESSION] Creating new session in database: ${currentSessionId}`);
        await ctx.runMutation(internal.queries.createSession, {
          userId: args.userId,
          sessionId: currentSessionId,
          title: sessionTitle,
          conversationHistory: JSON.stringify(result.conversationHistory),
        });
      } else {
        console.log(`[SESSION] Updating existing session: ${currentSessionId}`);
        await ctx.runMutation(internal.queries.updateSession, {
          sessionId: currentSessionId,
          conversationHistory: JSON.stringify(result.conversationHistory),
          messageCount: result.conversationHistory.length,
        });
      }

      // Use the final response from the result
      const finalResponse = result.finalResponse;

      // Save the search to the database with session link
      await ctx.runMutation(internal.queries.insertSearch, {
        userId: args.userId,
        sessionId: currentSessionId,
        query: args.query,
        results: JSON.stringify({
          conversationHistory: result.conversationHistory,
          toolCalls: result.totalToolCalls,
          sourcesScraped: result.sourcesScraped,
          rounds: result.rounds
        }),
        aiResponse: finalResponse,
        timestamp: Date.now(),
      });

      // Final result is already handled by handleConversationWithProgress
      // Just update with the final result data
      await ctx.runMutation(internal.queries.updateSearchProgress, {
        searchId: args.searchId,
        step: "complete",
        progress: 100,
        message: `Search completed! Found ${result.totalToolCalls} tool calls and ${result.sourcesScraped} sources.`,
        finalResult: {
          query: args.query,
          response: finalResponse,
          toolCalls: result.totalToolCalls,
          sourcesScraped: result.sourcesScraped,
          sessionId: currentSessionId,
          timestamp: Date.now()
        }
      });

      return {
        query: args.query,
        response: finalResponse,
        toolCalls: result.totalToolCalls,
        sourcesScraped: result.sourcesScraped,
        sessionId: currentSessionId,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error("Background search error:", error);

      // Update progress to show error
      await ctx.runMutation(internal.queries.updateSearchProgress, {
        searchId: args.searchId,
        step: "error",
        progress: 0,
        message: `Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      });

      throw error;
    }
  },
});

// Main action that the frontend calls - starts background search and returns immediately
export const searchWebWithProgress = action({
  args: {
    query: v.string(),
    searchId: v.string(),
    sessionId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("User must be authenticated");
    }

    try {
      // Clean up old completed progress records first
      await ctx.runMutation(internal.queries.cleanupOldSearchProgress, {});

      // Clear any existing progress for this search ID to prevent stale data
      await ctx.runMutation(internal.queries.clearSearchProgress, {
        searchId: args.searchId
      }); 

      // Initial progress update
      await ctx.runMutation(internal.queries.updateSearchProgress, {
        searchId: args.searchId,
        step: "starting",
        progress: 5,
        message: "Starting search..."
      });

      console.log(`[SCHEDULER] Scheduling background search for searchId: ${args.searchId}`);

      // Schedule background search to run independently
      await ctx.scheduler.runAfter(0, internal.search.runBackgroundSearch, {
        query: args.query,
        searchId: args.searchId,
        sessionId: args.sessionId,
        userId: userId,
      });

      // Return immediately with search started status
      return {
        searchId: args.searchId,
        status: "started",
        message: "Search started in background. Check progress for updates.",
        timestamp: Date.now()
      };
      
    } catch (error) {
      console.error("Search initiation error:", error);

      // Update progress to show error
      await ctx.runMutation(internal.queries.updateSearchProgress, {
        searchId: args.searchId,
        step: "error",
        progress: 0,
        message: `Failed to start search: ${error instanceof Error ? error.message : 'Unknown error'}`
      });

      throw new Error(`Failed to start search: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

// Convex action that implements your exact conversation logic
// export const conversationSearch = action({
//   args: {
//     query: v.string(),
//     conversationHistory: v.optional(v.array(v.any())),
//   },
//   handler: async (_ctx, args) => {
//     try {
//       // Use your exact handleConversation logic
//       const initialHistory = args.conversationHistory || [];
//       const finalHistory = await handleConversation(args.query, initialHistory);

//       // Extract the final response from the conversation
//       const lastEntry = finalHistory[finalHistory.length - 1];
//       let finalResponse = "No response generated";

//       if (lastEntry && lastEntry.role === "model") {
//         if (lastEntry.parts && lastEntry.parts.length > 0) {
//           finalResponse = lastEntry.parts[0].text || "No response generated";
//         }
//       }

//       return {
//         query: args.query,
//         conversationHistory: finalHistory,
//         finalResponse,
//         timestamp: Date.now()
//       };
//     } catch (error) {
//       console.error("Conversation search error:", error);
//       throw new Error(`Conversation search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
//     }
//   },
// });

// Simple search action (for basic queries without conversation)
// export const simpleSearch = action({
//   args: {
//     query: v.string(),
//   },
//   handler: async (_ctx, args) => {
//     try {
//       // Use your exact conversation logic but with empty history
//       const finalHistory = await handleConversation(args.query, []);

//       // Extract the final response
//       const lastEntry = finalHistory[finalHistory.length - 1];
//       let finalResponse = "No response generated";

//       if (lastEntry && lastEntry.role === "model") {
//         if (lastEntry.parts && lastEntry.parts.length > 0) {
//           finalResponse = lastEntry.parts[0].text || "No response generated";
//         }
//       }

//       return {
//         query: args.query,
//         response: finalResponse,
//         timestamp: Date.now()
//       };
//     } catch (error) {
//       console.error("Simple search error:", error);
//       throw new Error(`Simple search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
//     }
//   },
// });

// Export the core functions for potential use in other parts of the application
export { performSerperSearch, performSerperScrape };